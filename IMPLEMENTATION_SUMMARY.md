# 看剧基金功能重构总结

## 📋 功能概述

根据后端API文档要求，重新设计了看剧基金设置功能，将原来的简单金额设置改为基于工作参数的实时工资计算系统。

## 🔧 主要变更

### 1. API类型定义更新 (`src/api/userReminder.ts`)

**新增类型定义：**
```typescript
export interface TheaterFundSettings {
  monthlySalary: number     // 月工资
  workStartTime: string     // 上班时间 "HH:mm"
  workEndTime: string       // 下班时间 "HH:mm"
  includeWeekends: boolean  // 是否包含周末
}
```

**更新响应接口：**
- 移除：`theaterFundCurrentAmount`、`theaterFundTargetAmount`
- 新增：`theaterFundSettings: TheaterFundSettings | null`

### 2. useUserReminder Composable 重构 (`src/composables/useUserReminder.ts`)

**新增核心算法：**

#### `calculateTodayEarnings()` - 计算今天赚了多少钱
- 检查是否为工作日（根据includeWeekends设置）
- 计算当前时间在工作时间内的分钟数
- 基于月工资和工作时间计算每分钟工资
- 返回今天已赚取的金额

#### `calculateAccumulatedEarnings()` - 计算从发薪日第二天开始的累积工资
- 从上次发薪日的第二天开始计算
- 逐天累积工作日的工资
- 今天只计算到当前时间
- 支持包含/排除周末的设置

**算法逻辑：**
```
每分钟工资 = 月工资 / (每月工作天数 × 每日工作分钟数)
每月工作天数 = 包含周末 ? 30天 : 22天
每日工作分钟数 = (下班时间 - 上班时间) 的分钟数
```

### 3. 设置页面重构 (`src/pages/sub/account/settings.vue`)

**UI重新设计：**
- 月工资输入框
- 上班时间选择器（时间选择器）
- 下班时间选择器（时间选择器）
- 是否包含周末开关
- 实时预览：每分钟收入、每日工作时长

**新增功能：**
- 时间选择器支持（仅时间模式）
- 实时计算预览信息
- 自动保存设置

### 4. 首页实时显示 (`src/pages/homepage/index2.vue`)

**实时更新机制：**
- 每分钟检查是否在工作时间内
- 在工作时间内每分钟更新一次"今天赚了"金额
- 使用响应式计算属性实时显示

**显示逻辑：**
- 显示从发薪日第二天开始的累积工资
- 格式：`¥123.45`
- 无设置时显示：`¥0.00`

## 🎯 核心特性

### 1. 实时工资计算
- **精确到分钟**：在工作时间内每分钟更新一次累积金额
- **工作日判断**：支持包含/排除周末的灵活设置
- **发薪周期**：从发薪日第二天开始累积，到下次发薪日重置

### 2. 智能时间管理
- **工作时间检测**：只在设定的工作时间内累积工资
- **周末处理**：根据用户设置决定是否包含周末
- **实时响应**：页面显示随时间实时更新

### 3. 用户体验优化
- **直观设置**：简单的工资和时间参数设置
- **实时预览**：设置页面显示每分钟收入等预览信息
- **自动保存**：设置修改后自动保存到后端

## 📊 使用场景

### 场景1：普通上班族
- 月工资：8000元
- 工作时间：09:00-18:00
- 不包含周末
- **结果**：每分钟赚取约2.02元

### 场景2：自由职业者
- 月工资：12000元
- 工作时间：10:00-22:00
- 包含周末
- **结果**：每分钟赚取约2.78元

## 🔄 数据流程

1. **设置阶段**：用户在设置页面配置工作参数
2. **存储阶段**：参数以JSON格式存储到后端
3. **计算阶段**：前端实时计算累积工资
4. **显示阶段**：首页实时显示"今天赚了"金额

## 🚀 技术亮点

1. **响应式设计**：使用Vue 3的响应式系统实现实时更新
2. **精确计算**：基于dayjs进行精确的时间计算
3. **性能优化**：合理的更新频率（每分钟）平衡性能和实时性
4. **类型安全**：完整的TypeScript类型定义
5. **用户友好**：直观的UI设计和实时预览功能

## 📝 后续优化建议

1. **数据持久化**：考虑本地缓存计算结果，减少重复计算
2. **通知提醒**：在达到特定金额时发送通知
3. **统计分析**：添加周/月收入统计图表
4. **目标设定**：允许用户设定收入目标并显示进度
5. **多币种支持**：支持不同货币单位的显示

## ✅ 完成状态

- [x] API类型定义更新
- [x] useUserReminder composable重构
- [x] 设置页面UI重新设计
- [x] 首页实时显示功能
- [x] 实时工资计算算法
- [x] 时间选择器集成
- [x] 响应式更新机制

所有核心功能已完成，可以正常使用。用户可以在设置页面配置工作参数，首页会实时显示基于工作时间的累积工资。
