<template>
  <view class="page-container">
    <!-- 导航栏 -->
    <u-navbar class="w-full shrink-0" title="提醒小助手" :titleStyle="titleStyle" @leftClick="handleBack" bgColor="transparent" leftIconColor="#1a1a1a" placeholder />

    <!-- 滚动容器 -->
    <scroll-view class="scroll-container" scroll-y enable-back-to-top>
      <view class="content-container">
        <!-- 提醒设置列表 -->
        <view class="settings-list">
          <!-- 发薪日设置 -->
          <view class="setting-card">
            <view class="setting-main">
              <view class="setting-header">
                <view class="setting-info">
                  <view class="setting-icon">
                    <text class="fas fa-dollar-sign"></text>
                  </view>
                  <view class="setting-text">
                    <text class="setting-title">发薪日提醒</text>
                    <text class="setting-description">设置每月发薪日期</text>
                  </view>
                </view>
              </view>

              <view class="setting-config">
                <view class="salary-config">
                  <text class="config-label">每月</text>
                  <view class="input-wrapper">
                    <u-input v-model="reminderSettings.salaryDay" type="number" placeholder="25" class="salary-input" border="none" @change="handleSalaryDayChange" />
                  </view>
                  <text class="config-label">号发薪</text>
                </view>
              </view>
            </view>
          </view>

          <!-- 开演时间设置 -->
          <view class="setting-card">
            <view class="setting-main">
              <view class="setting-header">
                <view class="setting-info">
                  <view class="setting-icon">
                    <text class="fas fa-theater-masks"></text>
                  </view>
                  <view class="setting-text">
                    <text class="setting-title">开演时间提醒</text>
                    <text class="setting-description">设置即将观看的演出时间</text>
                  </view>
                </view>
              </view>

              <view class="setting-config">
                <view class="datetime-picker" @tap="showDateTimePickerHandler('showTime')">
                  <text class="datetime-text">
                    {{ reminderSettings.showTime ? formatDateTime(reminderSettings.showTime) : '点击选择演出时间' }}
                  </text>
                </view>
              </view>
            </view>
          </view>

          <!-- 开票时间设置 -->
          <view class="setting-card">
            <view class="setting-main">
              <view class="setting-header">
                <view class="setting-info">
                  <view class="setting-icon">
                    <text class="fas fa-ticket-alt"></text>
                  </view>
                  <view class="setting-text">
                    <text class="setting-title">开票时间提醒</text>
                    <text class="setting-description">设置关注演出的开票时间</text>
                  </view>
                </view>
              </view>

              <view class="setting-config">
                <view class="datetime-picker" @tap="showDateTimePickerHandler('ticketSaleTime')">
                  <text class="datetime-text">
                    {{ reminderSettings.ticketSaleTime ? formatDateTime(reminderSettings.ticketSaleTime) : '点击选择开票时间' }}
                  </text>
                </view>
              </view>
            </view>
          </view>

          <!-- 看剧基金设置 -->
          <view class="setting-card">
            <view class="setting-main">
              <view class="setting-header">
                <view class="setting-info">
                  <view class="setting-icon">
                    <text class="fas fa-piggy-bank"></text>
                  </view>
                  <view class="setting-text">
                    <text class="setting-title">看剧基金</text>
                    <text class="setting-description">设置工资参数，计算每日收入</text>
                  </view>
                </view>
              </view>

              <view class="setting-config">
                <view class="fund-config">
                  <!-- 月工资设置 -->
                  <view class="fund-input-group">
                    <text class="fund-label">月工资</text>
                    <view class="fund-input-wrapper">
                      <text class="currency-symbol">¥</text>
                      <u-input v-model="theaterFundSettings.monthlySalary" type="number" placeholder="8000" class="fund-input" border="none" @change="handleTheaterFundChange" />
                    </view>
                  </view>

                  <!-- 上班时间设置 -->
                  <view class="fund-input-group">
                    <text class="fund-label">上班时间</text>
                    <view class="time-picker-wrapper" @tap="showTimePickerHandler('workStartTime')">
                      <text class="time-text">{{ theaterFundSettings.workStartTime || '09:00' }}</text>
                      <text class="fas fa-clock time-icon"></text>
                    </view>
                  </view>

                  <!-- 下班时间设置 -->
                  <view class="fund-input-group">
                    <text class="fund-label">下班时间</text>
                    <view class="time-picker-wrapper" @tap="showTimePickerHandler('workEndTime')">
                      <text class="time-text">{{ theaterFundSettings.workEndTime || '18:00' }}</text>
                      <text class="fas fa-clock time-icon"></text>
                    </view>
                  </view>

                  <!-- 是否包含周末 -->
                  <view class="fund-input-group">
                    <text class="fund-label">包含周末</text>
                    <view class="switch-wrapper">
                      <u-switch v-model="theaterFundSettings.includeWeekends" @change="handleTheaterFundChange" />
                    </view>
                  </view>

                  <!-- 预览信息 -->
                  <view class="preview-section" v-if="theaterFundSettings.monthlySalary">
                    <view class="preview-header">
                      <text class="preview-label">预览信息</text>
                    </view>
                    <view class="preview-item">
                      <text class="preview-text">每分钟收入：¥{{ calculateMinutelyIncome() }}</text>
                    </view>
                    <view class="preview-item">
                      <text class="preview-text">每日工作时长：{{ calculateDailyWorkHours() }}小时</text>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 使用说明 -->
        <view class="help-card">
          <view class="help-header">
            <view class="help-icon">
              <text class="fas fa-info-circle"></text>
            </view>
            <text class="help-title">使用说明</text>
          </view>
          <view class="help-content">
            <view class="help-item">
              <text class="help-bullet">•</text>
              <text class="help-text">最紧急的倒计时将在主页大卡片中显示</text>
            </view>
            <view class="help-item">
              <text class="help-bullet">•</text>
              <text class="help-text">其他启用的倒计时在小网格中显示</text>
            </view>
            <view class="help-item">
              <text class="help-bullet">•</text>
              <text class="help-text">倒计时按距离天数自动排序优先级</text>
            </view>
          </view>
        </view>

        <!-- 底部操作提示 -->
        <view class="bottom-tip">
          <text class="tip-text">设置完成后，返回主页查看倒计时显示</text>
        </view>
      </view>
    </scroll-view>

    <!-- toast提示 -->
    <u-toast ref="uToast"></u-toast>

    <!-- 时间选择器 -->
    <u-datetime-picker
      :show="showDateTimePicker"
      :value="getCurrentPickerValue()"
      mode="datetime"
      :min-date="dayjs().add(0, 'day').valueOf()"
      :max-date="dayjs().add(2, 'year').valueOf()"
      @confirm="confirmDateTime"
      @cancel="showDateTimePicker = false" />

    <!-- 时间选择器（仅时间） -->
    <u-datetime-picker :show="showTimePicker" :value="getCurrentTimePickerValue()" mode="time" @confirm="confirmTime" @cancel="showTimePicker = false" />
  </view>
</template>

<script lang="ts" setup>
import type { UserReminderRequest } from '@/api/userReminder'
import { useUserReminder } from '@/composables/useUserReminder'
import { onLoad } from '@dcloudio/uni-app'
import dayjs from 'dayjs'
import { onMounted, ref, watch } from 'vue'

const uToast = ref()
const showDateTimePicker = ref(false)
const showTimePicker = ref(false)
const currentPickerKey = ref('')
const currentTimePickerKey = ref('')

// 用户提醒功能
const { data: reminderData, loading: reminderLoading, refresh: refreshReminder, save: saveReminder, updateTheaterFundSettings } = useUserReminder()

// 提醒设置数据
const reminderSettings = ref({
  salaryDay: 25,
  showTime: '',
  ticketSaleTime: ''
})

// 看剧基金设置数据
const theaterFundSettings = ref({
  monthlySalary: 8000,
  workStartTime: '09:00',
  workEndTime: '18:00',
  includeWeekends: false
})

// 监听API数据变化，更新本地设置
watch(
  reminderData,
  newData => {
    if (newData) {
      reminderSettings.value = {
        salaryDay: newData.salaryDay || 25,
        showTime: newData.showTime || '',
        ticketSaleTime: newData.ticketSaleTime || ''
      }

      // 更新看剧基金设置
      if (newData.theaterFundSettings) {
        theaterFundSettings.value = {
          monthlySalary: newData.theaterFundSettings.monthlySalary || 8000,
          workStartTime: newData.theaterFundSettings.workStartTime || '09:00',
          workEndTime: newData.theaterFundSettings.workEndTime || '18:00',
          includeWeekends: newData.theaterFundSettings.includeWeekends || false
        }
      }
    }
  },
  { immediate: true }
)

// 标题样式
const titleStyle = {
  color: '#1a1a1a',
  fontSize: '36rpx',
  fontWeight: '600'
}

// 计算每分钟收入
const calculateMinutelyIncome = () => {
  const { monthlySalary, workStartTime, workEndTime, includeWeekends } = theaterFundSettings.value
  if (!monthlySalary || !workStartTime || !workEndTime) return '0.00'

  const [startHour, startMinute] = workStartTime.split(':').map(Number)
  const [endHour, endMinute] = workEndTime.split(':').map(Number)

  const dailyWorkMinutes = endHour * 60 + endMinute - (startHour * 60 + startMinute)
  const workDaysPerMonth = includeWeekends ? 30 : 22
  const salaryPerMinute = monthlySalary / (workDaysPerMonth * dailyWorkMinutes)

  return salaryPerMinute.toFixed(2)
}

// 计算每日工作时长
const calculateDailyWorkHours = () => {
  const { workStartTime, workEndTime } = theaterFundSettings.value
  if (!workStartTime || !workEndTime) return '0'

  const [startHour, startMinute] = workStartTime.split(':').map(Number)
  const [endHour, endMinute] = workEndTime.split(':').map(Number)

  const totalMinutes = endHour * 60 + endMinute - (startHour * 60 + startMinute)
  const hours = Math.floor(totalMinutes / 60)
  const minutes = totalMinutes % 60

  return minutes > 0 ? `${hours}.${Math.round((minutes / 60) * 10)}` : `${hours}`
}

onMounted(() => {
  uni.getSystemInfo({
    success: res => {
      console.log('系统信息:', res)
    }
  })
})

/* 返回处理 */
const handleBack = () => {
  uni.navigateBack({
    delta: 1,
    fail: () => {
      uni.reLaunch({
        url: '/pages/homepage/index2'
      })
    }
  })
}

/* 显示时间选择器 */
const showDateTimePickerHandler = (key: string) => {
  currentPickerKey.value = key
  showDateTimePicker.value = true
}

/* 显示时间选择器（仅时间） */
const showTimePickerHandler = (key: string) => {
  currentTimePickerKey.value = key
  showTimePicker.value = true
}

onLoad(() => {
  loadSettings()
})

/* 加载设置 */
const loadSettings = async () => {
  await refreshReminder()
}

/* 发薪日变化处理 */
const handleSalaryDayChange = async () => {
  const requestData: UserReminderRequest = {
    salaryDay: Number(reminderSettings.value.salaryDay) || 25
  }

  const success = await saveReminder(requestData)
  if (!success) {
    uni.showToast({
      title: '保存失败，请重试',
      icon: 'none',
      duration: 2000
    })
  }
}

/* 看剧基金设置变化处理 */
const handleTheaterFundChange = async () => {
  const success = await updateTheaterFundSettings(theaterFundSettings.value)
  if (success) {
    uni.showToast({
      title: '保存成功',
      icon: 'success',
      duration: 1500
    })
  } else {
    uni.showToast({
      title: '保存失败，请重试',
      icon: 'none',
      duration: 2000
    })
  }
}

/* 确认选择时间 */
const confirmDateTime = async (e: any) => {
  if (currentPickerKey.value) {
    // 将时间戳转换为标准格式
    const selectedTime = dayjs(e.value).format('YYYY-MM-DD HH:mm:ss')
    const requestData: UserReminderRequest = {}

    if (currentPickerKey.value === 'showTime') {
      reminderSettings.value.showTime = selectedTime
      requestData.showTime = selectedTime
    } else if (currentPickerKey.value === 'ticketSaleTime') {
      reminderSettings.value.ticketSaleTime = selectedTime
      requestData.ticketSaleTime = selectedTime
    }

    const success = await saveReminder(requestData)
    if (success) {
      uni.showToast({
        title: '保存成功',
        icon: 'success',
        duration: 1500
      })
    } else {
      uni.showToast({
        title: '保存失败，请重试',
        icon: 'none',
        duration: 2000
      })
    }
  }
  showDateTimePicker.value = false
  currentPickerKey.value = ''
}

/* 获取当前选择器的值 - 如果没有设置则返回当前时间 */
const getCurrentPickerValue = () => {
  if (!currentPickerKey.value) return dayjs().valueOf()

  if (currentPickerKey.value === 'showTime') {
    return reminderSettings.value.showTime ? dayjs(reminderSettings.value.showTime).valueOf() : dayjs().valueOf()
  } else if (currentPickerKey.value === 'ticketSaleTime') {
    return reminderSettings.value.ticketSaleTime ? dayjs(reminderSettings.value.ticketSaleTime).valueOf() : dayjs().valueOf()
  }

  return dayjs().valueOf()
}

/* 获取默认选择器值 - 始终返回当前时间 */
const getDefaultPickerValue = () => {
  return dayjs().valueOf()
}

/* 获取当前时间选择器的值 */
const getCurrentTimePickerValue = () => {
  if (!currentTimePickerKey.value) return dayjs().valueOf()

  const timeStr = theaterFundSettings.value[currentTimePickerKey.value as keyof typeof theaterFundSettings.value] as string
  if (timeStr) {
    const [hour, minute] = timeStr.split(':').map(Number)
    return dayjs().hour(hour).minute(minute).second(0).valueOf()
  }

  return dayjs().valueOf()
}

/* 确认选择时间（仅时间） */
const confirmTime = async (e: any) => {
  if (currentTimePickerKey.value) {
    const selectedTime = dayjs(e.value).format('HH:mm')

    if (currentTimePickerKey.value === 'workStartTime') {
      theaterFundSettings.value.workStartTime = selectedTime
    } else if (currentTimePickerKey.value === 'workEndTime') {
      theaterFundSettings.value.workEndTime = selectedTime
    }

    // 自动保存
    await handleTheaterFundChange()
  }
  showTimePicker.value = false
  currentTimePickerKey.value = ''
}

/* 格式化日期时间 - 显示完整时间包含年月日时分 */
const formatDateTime = (datetime: string | undefined) => {
  if (!datetime) return ''
  return dayjs(datetime).format('YYYY年MM月DD日 HH:mm')
}
</script>

<style lang="scss" scoped>
@import '@/uni_modules/uv-ui-tools/libs/css/variable.scss';

.page-container {
  height: 100vh;
  background: linear-gradient(180deg, #fafafa 0%, #f5f5f5 100%);
  position: relative;
  display: flex;
  flex-direction: column;
}

/* 滚动容器 */
.scroll-container {
  flex: 1;
  height: auto;
}

.content-container {
  padding: 0 24rpx 40rpx;
  margin-bottom: 200rpx;
}

/* 设置列表 */
.settings-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.setting-card {
  background: #ffffff;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.04);
  border: 1px solid #f0f0f0;
}

.setting-main {
  padding: 32rpx;
}

.setting-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.setting-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.setting-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 56rpx;
  height: 56rpx;
  background: linear-gradient(135deg, #9333ea 0%, #764ba2 100%);
  border-radius: 14rpx;
  margin-right: 20rpx;

  .fas {
    font-size: 28rpx;
    color: #ffffff;
  }
}

.setting-text {
  flex: 1;
}

.setting-title {
  display: block;
  font-size: 30rpx;
  font-weight: 600;
  color: #1a1a1a;
  line-height: 38rpx;
  margin-bottom: 4rpx;
}

.setting-description {
  display: block;
  font-size: 24rpx;
  color: #666666;
  line-height: 32rpx;
}

/* 配置区域 */
.setting-config {
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-top: 8rpx;
}

.config-section {
  width: 100%;
}

/* 发薪日配置 */
.salary-config {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
}

.config-label {
  font-size: 28rpx;
  color: #1a1a1a;
  font-weight: 500;
}

.input-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 120rpx;
  height: 64rpx;
  background: #ffffff;
  border-radius: 12rpx;
  border: 1px solid #e5e7eb;
}

.salary-input {
  text-align: center;
  font-size: 28rpx;
  font-weight: 600;
  color: #1a1a1a;
}

/* 日期时间选择器 */
.datetime-picker {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 64rpx;
  background: #ffffff;
  border-radius: 12rpx;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
    border-color: #9333ea;
  }
}

.datetime-text {
  font-size: 28rpx;
  font-weight: 500;
  color: #1a1a1a;
}

/* 基金配置 */
.fund-config {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.fund-input-group {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.fund-label {
  font-size: 26rpx;
  color: #1a1a1a;
  font-weight: 500;
  width: 140rpx;
  flex-shrink: 0;
}

.fund-input-wrapper {
  display: flex;
  align-items: center;
  flex: 1;
  height: 64rpx;
  background: #ffffff;
  border-radius: 12rpx;
  border: 1px solid #e5e7eb;
  padding: 0 16rpx;
}

.currency-symbol {
  font-size: 24rpx;
  color: #666666;
  margin-right: 8rpx;
}

.fund-input {
  flex: 1;
  font-size: 26rpx;
  font-weight: 500;
  color: #1a1a1a;
}

/* 时间选择器 */
.time-picker-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex: 1;
  height: 64rpx;
  background: #ffffff;
  border-radius: 12rpx;
  border: 1px solid #e5e7eb;
  padding: 0 16rpx;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
    border-color: #9333ea;
  }
}

.time-text {
  font-size: 26rpx;
  font-weight: 500;
  color: #1a1a1a;
}

.time-icon {
  font-size: 20rpx;
  color: #666666;
}

/* 开关包装器 */
.switch-wrapper {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  flex: 1;
}

/* 预览信息 */
.preview-section {
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 1px solid #f0f0f0;
}

.preview-header {
  margin-bottom: 12rpx;
}

.preview-label {
  font-size: 24rpx;
  font-weight: 600;
  color: #1a1a1a;
}

.preview-item {
  margin-bottom: 8rpx;
}

.preview-text {
  font-size: 22rpx;
  color: #666666;
  line-height: 30rpx;
}

/* 帮助卡片 */
.help-card {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 24rpx;
  margin-top: 40rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.03);
  border: 1px solid #f0f0f0;
}

.help-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.help-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32rpx;
  height: 32rpx;
  margin-right: 12rpx;

  .fas {
    font-size: 24rpx;
    color: #9333ea;
  }
}

.help-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #1a1a1a;
}

.help-content {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.help-item {
  display: flex;
  align-items: flex-start;
  line-height: 32rpx;
}

.help-bullet {
  font-size: 22rpx;
  color: #9333ea;
  margin-right: 12rpx;
  margin-top: 2rpx;
  flex-shrink: 0;
}

.help-text {
  font-size: 22rpx;
  color: #666666;
  line-height: 32rpx;
  flex: 1;
}

/* 底部提示 */
.bottom-tip {
  text-align: center;
  margin-top: 24rpx;
  margin-bottom: 24rpx;
}

.tip-text {
  font-size: 24rpx;
  color: #999999;
  line-height: 34rpx;
}

/* 交互效果 */
.setting-card {
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
  }
}

/* 深度样式覆盖 */
:deep(.u-input__content__field-wrapper__field) {
  text-align: center;
}
</style>
