/* 用户提醒功能 API - 简化版 */

/* 获取用户提醒设置 */
export const $getUserReminder = () => uni.$u.http.get('/userReminder/getUserReminder')

/* 保存/更新用户提醒设置 */
export const $saveOrUpdateUserReminder = (data: any) => uni.$u.http.post('/userReminder/saveOrUpdateUserReminder', data)

/* 删除用户提醒设置 */
export const $deleteUserReminder = () => uni.$u.http.delete('/userReminder/deleteUserReminder')

/* 更新看剧基金金额 */
export const $updateTheaterFund = (params: any) => uni.$u.http.post('/userReminder/updateTheaterFund', {}, { params })

// 看剧基金设置类型
export interface TheaterFundSettings {
  monthlySalary: number // 月工资
  workStartTime: string // 上班时间 "HH:mm"
  workEndTime: string // 下班时间 "HH:mm"
  includeWeekends: boolean // 是否包含周末
}

// TypeScript 类型定义
export interface UserReminderResponse {
  id: number
  userId: number
  salaryDay: number // 发薪日 1-31
  showTime: string // 开演时间 "yyyy-MM-dd HH:mm:ss"
  ticketSaleTime: string // 开票时间 "yyyy-MM-dd HH:mm:ss"
  theaterFundSettings: TheaterFundSettings | null // 看剧基金设置
  createTime: string
}

export interface UserReminderRequest {
  salaryDay?: number // 发薪日 1-31
  showTime?: string // 开演时间 "yyyy-MM-dd HH:mm:ss"
  ticketSaleTime?: string // 开票时间 "yyyy-MM-dd HH:mm:ss"
  theaterFundSettings?: TheaterFundSettings // 看剧基金设置
}
