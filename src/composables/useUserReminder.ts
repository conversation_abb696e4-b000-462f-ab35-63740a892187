import { $getUserReminder, $saveOrUpdateUserReminder, type TheaterFundSettings, type UserReminderRequest, type UserReminderResponse } from '@/api/userReminder'
import dayjs from 'dayjs'
import { computed, readonly, ref } from 'vue'

export const useUserReminder = () => {
  const data = ref<UserReminderResponse | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 获取用户提醒设置
  const refresh = async () => {
    loading.value = true
    error.value = null
    try {
      const response = await $getUserReminder()
      if (response.code === 200) {
        data.value = response.data
      } else {
        error.value = response.msg || '获取数据失败'
      }
    } catch (err: any) {
      error.value = err.msg || '网络请求失败'
      console.error('获取用户提醒设置失败:', err)
    } finally {
      loading.value = false
    }
  }

  // 保存用户提醒设置
  const save = async (requestData: UserReminderRequest): Promise<boolean> => {
    try {
      const response = await $saveOrUpdateUserReminder(requestData)
      if (response.code === 200) {
        await refresh() // 刷新数据
        return true
      } else {
        error.value = response.msg || '保存失败'
        return false
      }
    } catch (err: any) {
      error.value = err.msg || '保存失败'
      console.error('保存用户提醒设置失败:', err)
      return false
    }
  }

  // 更新看剧基金设置
  const updateTheaterFundSettings = async (settings: TheaterFundSettings): Promise<boolean> => {
    try {
      const requestData: UserReminderRequest = {
        theaterFundSettings: settings
      }

      const response = await $saveOrUpdateUserReminder(requestData)
      if (response.code === 200) {
        await refresh() // 刷新数据
        return true
      } else {
        error.value = response.msg || '更新失败'
        return false
      }
    } catch (err: any) {
      error.value = err.msg || '更新失败'
      console.error('更新看剧基金设置失败:', err)
      return false
    }
  }

  // 计算详细的倒计时信息（天时分秒）
  const calculateDetailedCountdown = (targetTime: any) => {
    const now = dayjs()
    const target = dayjs(targetTime)
    const diff = target.valueOf() - now.valueOf()

    if (diff <= 0) return null

    const days = Math.floor(diff / (1000 * 60 * 60 * 24))
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
    const seconds = Math.floor((diff % (1000 * 60)) / 1000)

    return { days, hours, minutes, seconds, totalDays: days }
  }

  // 计算今天赚了多少钱
  const calculateTodayEarnings = (fundSettings: TheaterFundSettings | null) => {
    if (!fundSettings) return 0

    const { monthlySalary, workStartTime, workEndTime: workEndTimeStr, includeWeekends } = fundSettings
    const now = dayjs()

    // 检查是否是工作日
    const isWeekend = now.day() === 0 || now.day() === 6 // 0=周日, 6=周六
    if (isWeekend && !includeWeekends) {
      return 0
    }

    // 解析工作时间
    const [startHour, startMinute] = workStartTime.split(':').map(Number)
    const [endHour, endMinute] = workEndTimeStr.split(':').map(Number)

    const workStart = now.hour(startHour).minute(startMinute).second(0)
    const workEnd = now.hour(endHour).minute(endMinute).second(0)

    // 如果当前时间在工作时间之前，返回0
    if (now.isBefore(workStart)) {
      return 0
    }

    // 计算工作时间（分钟）
    const actualWorkEndTime = now.isAfter(workEnd) ? workEnd : now
    const workedMinutes = actualWorkEndTime.diff(workStart, 'minute')

    if (workedMinutes <= 0) return 0

    // 计算每月工作天数
    const workDaysPerMonth = includeWeekends ? 30 : 22 // 包含周末30天，否则22个工作日

    // 计算每天工作分钟数
    const dailyWorkMinutes = workEnd.diff(workStart, 'minute')

    // 计算每分钟工资
    const salaryPerMinute = monthlySalary / (workDaysPerMonth * dailyWorkMinutes)

    // 计算今天已赚取的金额
    return Math.round(workedMinutes * salaryPerMinute * 100) / 100 // 保留两位小数
  }

  // 计算从发薪日第二天开始的累积工资
  const calculateAccumulatedEarnings = (fundSettings: TheaterFundSettings | null, salaryDay: number) => {
    if (!fundSettings) return 0

    const { monthlySalary, workStartTime, workEndTime: workEndTimeStr, includeWeekends } = fundSettings
    const now = dayjs()

    // 计算上次发薪日
    let lastSalaryDate = now.date(salaryDay)
    if (now.date() < salaryDay) {
      // 如果当前日期小于发薪日，说明上次发薪是上个月
      lastSalaryDate = now.subtract(1, 'month').date(salaryDay)
    }

    // 从发薪日第二天开始计算
    const startDate = lastSalaryDate.add(1, 'day')

    if (now.isBefore(startDate)) {
      return 0
    }

    let totalEarnings = 0
    let currentDate = startDate

    // 解析工作时间
    const [startHour, startMinute] = workStartTime.split(':').map(Number)
    const [endHour, endMinute] = workEndTimeStr.split(':').map(Number)

    // 计算每天工作分钟数
    const dailyWorkMinutes = endHour * 60 + endMinute - (startHour * 60 + startMinute)

    // 计算每月工作天数
    const workDaysPerMonth = includeWeekends ? 30 : 22

    // 计算每分钟工资
    const salaryPerMinute = monthlySalary / (workDaysPerMonth * dailyWorkMinutes)

    // 逐天计算累积工资
    while (currentDate.isBefore(now, 'day') || currentDate.isSame(now, 'day')) {
      const isWeekend = currentDate.day() === 0 || currentDate.day() === 6

      if (!isWeekend || includeWeekends) {
        if (currentDate.isSame(now, 'day')) {
          // 今天只计算到当前时间
          const workStart = currentDate.hour(startHour).minute(startMinute).second(0)
          const workEnd = currentDate.hour(endHour).minute(endMinute).second(0)

          if (now.isAfter(workStart)) {
            const actualWorkEndTime = now.isAfter(workEnd) ? workEnd : now
            const workedMinutes = actualWorkEndTime.diff(workStart, 'minute')
            if (workedMinutes > 0) {
              totalEarnings += workedMinutes * salaryPerMinute
            }
          }
        } else {
          // 过去的日子计算全天工资
          totalEarnings += dailyWorkMinutes * salaryPerMinute
        }
      }

      currentDate = currentDate.add(1, 'day')
    }

    return Math.round(totalEarnings * 100) / 100 // 保留两位小数
  }

  // 计算倒计时数据 - 用于首页显示
  const countdownData = computed(() => {
    if (!data.value) return null

    const reminders = []
    const now = dayjs()

    // 发薪日倒计时
    if (data.value.salaryDay) {
      const salaryDay = data.value.salaryDay
      const currentDate = now.date()

      // 计算下次发薪日
      let nextSalaryDate = now.date(salaryDay).hour(9).minute(0).second(0) // 假设9点发薪
      if (currentDate >= salaryDay) {
        // 如果本月发薪日已过，计算下月
        nextSalaryDate = now.add(1, 'month').date(salaryDay).hour(9).minute(0).second(0)
      }

      const diffDays = nextSalaryDate.diff(now, 'day')

      reminders.push({
        type: 'salary',
        title: '发薪倒计时',
        days: diffDays,
        value: `${diffDays}天`,
        priority: diffDays,
        targetTime: nextSalaryDate
      })
    }

    // 开演倒计时
    if (data.value.showTime) {
      const countdown = calculateDetailedCountdown(data.value.showTime)

      if (countdown) {
        reminders.push({
          type: 'showTime',
          title: '开演倒计时',
          days: countdown.totalDays,
          hours: countdown.hours,
          minutes: countdown.minutes,
          seconds: countdown.seconds,
          value: countdown.totalDays > 0 ? `${countdown.totalDays}天` : `${countdown.hours}时${countdown.minutes}分`,
          detailedValue: `${countdown.days}天${countdown.hours}时${countdown.minutes}分${countdown.seconds}秒`,
          priority: countdown.totalDays,
          targetTime: data.value.showTime,
          hasDetailedTime: true
        })
      }
    }

    // 开票倒计时
    if (data.value.ticketSaleTime) {
      const countdown = calculateDetailedCountdown(data.value.ticketSaleTime)

      if (countdown) {
        reminders.push({
          type: 'ticketSale',
          title: '开票倒计时',
          days: countdown.totalDays,
          hours: countdown.hours,
          minutes: countdown.minutes,
          seconds: countdown.seconds,
          value: countdown.totalDays > 0 ? `${countdown.totalDays}天` : `${countdown.hours}时${countdown.minutes}分`,
          detailedValue: `${countdown.days}天${countdown.hours}时${countdown.minutes}分${countdown.seconds}秒`,
          priority: countdown.totalDays,
          targetTime: data.value.ticketSaleTime,
          hasDetailedTime: true
        })
      }
    }

    // 看剧基金 - 今天赚了多少钱
    if (data.value.theaterFundSettings && data.value.salaryDay) {
      const todayEarnings = calculateAccumulatedEarnings(data.value.theaterFundSettings, data.value.salaryDay)

      reminders.push({
        type: 'fund',
        title: '今天赚了',
        value: `¥${todayEarnings.toFixed(2)}`, // 显示累积金额
        priority: 999 // 基金优先级较低
      })
    }

    // 按优先级排序（天数越少优先级越高）
    reminders.sort((a, b) => a.priority - b.priority)

    return {
      main: reminders[0] || null, // 主要显示的倒计时
      others: reminders.slice(1) // 其他倒计时
    }
  })

  return {
    data: readonly(data),
    loading: readonly(loading),
    error: readonly(error),
    countdownData,
    refresh,
    save,
    updateTheaterFundSettings,
    calculateTodayEarnings,
    calculateAccumulatedEarnings
  }
}
