# 用户提醒功能 - 前端API文档（JSON版）

## 📋 功能概述

用户提醒功能允许用户设置四种类型的提醒：

- 💰 **发薪日提醒**：设置发薪日期（1-31号）
- 🎭 **开演时间提醒**：设置具体的演出开始时间
- 🎫 **开票时间提醒**：设置具体的开票时间  
- 💳 **看剧基金提醒**：设置月工资、上下班时间、是否包含周末等参数（JSON格式存储）

---

## 🔗 API接口列表

### 1. 获取用户提醒设置

**接口信息**

```http
GET /userReminder/getUserReminder
```

**请求示例**

```javascript
const { data } = await axios.get('/userReminder/getUserReminder');
```

**响应数据结构**

```typescript
interface UserReminderResponse {
  id: number;
  userId: number;
  salaryDay: number;              // 发薪日 1-31
  showTime: string;               // 开演时间 "yyyy-MM-dd HH:mm:ss"
  ticketSaleTime: string;         // 开票时间 "yyyy-MM-dd HH:mm:ss"
  theaterFundSettings: {          // 看剧基金设置
    monthlySalary: number;        // 月工资
    workStartTime: string;        // 上班时间 "HH:mm"
    workEndTime: string;          // 下班时间 "HH:mm"
    includeWeekends: boolean;     // 是否包含周末
  } | null;
  createTime: string;
}
```

**响应示例**

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "id": 1,
    "userId": 123,
    "salaryDay": 15,
    "showTime": "2025-02-15 19:30:00",
    "ticketSaleTime": "2025-01-15 10:00:00",
    "theaterFundSettings": {
      "monthlySalary": 8000.00,
      "workStartTime": "09:00",
      "workEndTime": "18:00",
      "includeWeekends": false
    },
    "createTime": "2025-01-03 10:00:00"
  }
}
```

---

### 2. 保存/更新用户提醒设置

**接口信息**

```http
POST /userReminder/saveOrUpdateUserReminder
```

**请求参数类型**

```typescript
interface UserReminderRequest {
  salaryDay?: number;              // 发薪日 1-31
  showTime?: string;               // 开演时间 "yyyy-MM-dd HH:mm:ss"
  ticketSaleTime?: string;         // 开票时间 "yyyy-MM-dd HH:mm:ss"
  theaterFundSettings?: {          // 看剧基金设置
    monthlySalary: number;         // 月工资（必填，>0）
    workStartTime: string;         // 上班时间 "HH:mm"（必填）
    workEndTime: string;           // 下班时间 "HH:mm"（必填）
    includeWeekends: boolean;      // 是否包含周末（必填）
  };
}
```

**请求示例**

```javascript
const requestData = {
  salaryDay: 15,
  showTime: "2025-02-15 19:30:00",
  ticketSaleTime: "2025-01-15 10:00:00",
  theaterFundSettings: {
    monthlySalary: 8000.00,
    workStartTime: "09:00",
    workEndTime: "18:00",
    includeWeekends: false
  }
};

const response = await fetch('/userReminder/saveOrUpdateUserReminder', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(requestData)
});
```

**响应示例**

```json
{
  "code": 200,
  "msg": "操作成功"
}
```

---

### 3. 删除用户提醒设置

**接口信息**

```http
DELETE /userReminder/deleteUserReminder
```

**请求示例**

```javascript
const response = await fetch('/userReminder/deleteUserReminder', {
  method: 'DELETE',
  headers: {
    'Authorization': `Bearer ${token}`
  }
});
```

**响应示例**

```json
{
  "code": 200,
  "msg": "操作成功"
}
```

---

### 4. 更新看剧基金设置

**接口信息**

```http
POST /userReminder/updateTheaterFund
```

**请求参数**

```typescript
interface TheaterFundSettings {
  monthlySalary: number;         // 月工资（必填，>0）
  workStartTime: string;         // 上班时间 "HH:mm"（必填）
  workEndTime: string;           // 下班时间 "HH:mm"（必填）
  includeWeekends: boolean;      // 是否包含周末（必填）
}
```

**请求示例**

```javascript
const settings = {
  monthlySalary: 8000.00,
  workStartTime: "09:00",
  workEndTime: "18:00",
  includeWeekends: false
};

const response = await fetch('/userReminder/updateTheaterFund', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(settings)
});
```

**响应示例**

```json
{
  "code": 200,
  "msg": "操作成功"
}
```

---

## 🎨 前端实现建议

### React Hook 示例

```typescript
import { useState, useEffect } from 'react';

interface TheaterFundSettings {
  monthlySalary: number;
  workStartTime: string;
  workEndTime: string;
  includeWeekends: boolean;
}

interface UserReminderResponse {
  id: number;
  userId: number;
  salaryDay: number;
  showTime: string;
  ticketSaleTime: string;
  theaterFundSettings: TheaterFundSettings | null;
  createTime: string;
}

export const useUserReminder = () => {
  const [data, setData] = useState<UserReminderResponse | null>(null);
  const [loading, setLoading] = useState(false);

  const refresh = async () => {
    setLoading(true);
    try {
      const response = await fetch('/userReminder/getUserReminder');
      const result = await response.json();
      if (result.code === 200) {
        setData(result.data);
      }
    } catch (err) {
      console.error('获取提醒设置失败', err);
    } finally {
      setLoading(false);
    }
  };

  const save = async (requestData: any): Promise<boolean> => {
    try {
      const response = await fetch('/userReminder/saveOrUpdateUserReminder', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestData)
      });
      const result = await response.json();
      if (result.code === 200) {
        await refresh();
        return true;
      }
      return false;
    } catch (err) {
      console.error('保存失败', err);
      return false;
    }
  };

  const updateTheaterFund = async (settings: TheaterFundSettings): Promise<boolean> => {
    try {
      const response = await fetch('/userReminder/updateTheaterFund', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(settings)
      });
      const result = await response.json();
      if (result.code === 200) {
        await refresh();
        return true;
      }
      return false;
    } catch (err) {
      console.error('更新看剧基金设置失败', err);
      return false;
    }
  };

  useEffect(() => {
    refresh();
  }, []);

  return { data, loading, refresh, save, updateTheaterFund };
};
```

---

## ⚠️ 注意事项

### 参数验证规则

- `salaryDay`: 1-31之间的整数
- `showTime/ticketSaleTime`: 标准时间格式 "yyyy-MM-dd HH:mm:ss"
- `theaterFundSettings.monthlySalary`: 必须大于0的数字
- `theaterFundSettings.workStartTime/workEndTime`: 时间格式 "HH:mm"
- `theaterFundSettings.includeWeekends`: 布尔值

### 数据库表结构

```sql
CREATE TABLE `t_user_reminder` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `salary_day` int(2) DEFAULT NULL,
  `show_time` timestamp NULL DEFAULT NULL,
  `ticket_sale_time` timestamp NULL DEFAULT NULL,
  `theater_fund_settings` json DEFAULT NULL,
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_id` (`user_id`)
);
```

### JSON字段示例

```json
{
  "monthlySalary": 8000.00,
  "workStartTime": "09:00",
  "workEndTime": "18:00",
  "includeWeekends": false
}
```

### 错误处理

- `200`: 操作成功
- `500`: 服务器错误
- 具体错误信息在 `msg` 字段中返回

---

## 🚀 快速开始

1. **获取设置**：页面加载时调用获取接口
2. **保存设置**：表单提交时调用保存接口
3. **更新看剧基金**：单独更新看剧基金设置

## 💡 JSON格式优势

1. **灵活扩展**：可随时添加新字段而无需修改数据库表结构
2. **向后兼容**：现有字段保持不变，新增字段不影响旧版本
3. **类型安全**：前端可以定义完整的TypeScript接口
4. **易于维护**：所有看剧基金相关设置集中在一个JSON对象中

## 📝 使用示例

### 完整的表单提交示例

```javascript
// 表单数据
const formData = {
  salaryDay: 15,
  showTime: "2025-02-15 19:30:00",
  ticketSaleTime: "2025-01-15 10:00:00",
  theaterFundSettings: {
    monthlySalary: 8000.00,
    workStartTime: "09:00",
    workEndTime: "18:00",
    includeWeekends: false
  }
};

// 提交表单
const submitForm = async () => {
  try {
    const response = await fetch('/userReminder/saveOrUpdateUserReminder', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(formData)
    });

    const result = await response.json();
    if (result.code === 200) {
      console.log('保存成功');
    } else {
      console.error('保存失败:', result.msg);
    }
  } catch (error) {
    console.error('网络错误:', error);
  }
};
```

### Vue.js 使用示例

```javascript
// Vue 3 Composition API
import { ref, onMounted } from 'vue';

export function useUserReminder() {
  const data = ref(null);
  const loading = ref(false);

  const fetchData = async () => {
    loading.value = true;
    try {
      const response = await fetch('/userReminder/getUserReminder');
      const result = await response.json();
      if (result.code === 200) {
        data.value = result.data;
      }
    } catch (error) {
      console.error('获取数据失败:', error);
    } finally {
      loading.value = false;
    }
  };

  onMounted(() => {
    fetchData();
  });

  return {
    data,
    loading,
    fetchData
  };
}
```

这份JSON版API文档提供了完整的接口说明和使用示例，支持灵活的看剧基金设置扩展。
